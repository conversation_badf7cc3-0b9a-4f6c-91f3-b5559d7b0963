# 当前任务

## 任务描述

读取`.cursor/docs/DESIGN.md`设计文档, 根据文档内容结合项目规范文件和`API_DESIGN.md`API设计文档, 初始化`rbac`模块, 并实现相关功能.


## 要求
- 读取`k8s.io/api/rbac/v1/types.go`,`k8s.io/api/core/v1/types.go`,`k8s.io/apimachinery/pkg/apis/meta/v1/types.go`, 如果文件中有符合接口中的实体，优先使用`kubernetes`官方的结构体。
- 每次完成一个任务后, 检查当前任务是否完成, 并更新任务列表.

## 任务列表
- [ ] 基于`API_DESIGN.md`中定义的对象，在`models/rabc/types.go`文件中初始化所有的结构体, 
- [ ] 在`handler/rbac`文件夹中，初始化每个相关的操作处理器`handler`
- [ ] 初始化`ClusterRole`,`Role`,`ClusterRoleBinding`,`RoleBinding`,`ServiceAccount`相关处理器
- [ ] 初始化时，应该先定义`Hanlder`相关的接口。
- [ ] 基于`API_DESIGN.md`在`router/rbac`初始化`集群空间API`,`工作空间API`,`平台API相关接口`
- [ ] 初始化之后按照对应顺序
- [ ] 完成后,检查当前任务是否完成