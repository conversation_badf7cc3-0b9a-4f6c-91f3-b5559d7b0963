package constants

const (
	// RBAC API Group
	RBACAPIGroup = "rbac.authorization.k8s.io"

	// RBAC API Version
	RBACAPIVersion = "v1"

	// RBAC Resource Types
	RoleResourceType               = "roles"
	ClusterRoleResourceType        = "clusterroles"
	RoleBindingResourceType        = "rolebindings"
	ClusterRoleBindingResourceType = "clusterrolebindings"
	ServiceAccountResourceType     = "serviceaccounts"

	// RBAC Resource Kinds
	RoleKind               = "Role"
	ClusterRoleKind        = "ClusterRole"
	RoleBindingKind        = "RoleBinding"
	ClusterRoleBindingKind = "ClusterRoleBinding"
	ServiceAccountKind     = "ServiceAccount"

	// Subject Kinds (Kubernetes原生)
	UserSubjectKind           = "User"
	GroupSubjectKind          = "Group"
	ServiceAccountSubjectKind = "ServiceAccount"
)

const (
	// 平台RBAC前缀
	PlatformRBACPrefix = "rbac.hmc.cn"

	// 平台实体类型
	PlatformTypeTenant  = "tenant"
	PlatformTypeProject = "project"
	PlatformTypeAccount = "account"
	PlatformTypeRole    = "role"
)

const (
	// 系统保护标签
	SystemProtectedLabel = "rbac.olympus.io/system-protected"
	SystemProtectedValue = "true"

	// 平台管理标签
	PlatformManagedLabel = "rbac.olympus.io/platform-managed"
	PlatformManagedValue = "true"
)

const (
	// 系统命名空间（不允许删除ServiceAccount）
	KubeSystemNamespace    = "kube-system"
	KubePublicNamespace    = "kube-public"
	KubeNodeLeaseNamespace = "kube-node-lease"

	// 默认ServiceAccount名称（不允许删除）
	DefaultServiceAccountName = "default"
)

const (
	// 常用动作
	VerbGet              = "get"
	VerbList             = "list"
	VerbWatch            = "watch"
	VerbCreate           = "create"
	VerbUpdate           = "update"
	VerbPatch            = "patch"
	VerbDelete           = "delete"
	VerbDeleteCollection = "deletecollection"
	VerbAll              = "*"
)

const (
	// 常用API组
	CoreAPIGroup       = ""
	AppsAPIGroup       = "apps"
	ExtensionsAPIGroup = "extensions"
	BatchAPIGroup      = "batch"
	NetworkingAPIGroup = "networking.k8s.io"
	StorageAPIGroup    = "storage.k8s.io"
	APIGroupAll        = "*"
)

const (
	// 常用资源
	PodsResource                   = "pods"
	ServicesResource               = "services"
	ConfigMapsResource             = "configmaps"
	SecretsResource                = "secrets"
	NamespacesResource             = "namespaces"
	NodesResource                  = "nodes"
	PersistentVolumesResource      = "persistentvolumes"
	PersistentVolumeClaimsResource = "persistentvolumeclaims"
	DeploymentsResource            = "deployments"
	StatefulSetsResource           = "statefulsets"
	DaemonSetsResource             = "daemonsets"
	JobsResource                   = "jobs"
	CronJobsResource               = "cronjobs"
	IngressesResource              = "ingresses"
	ResourceAll                    = "*"
)

const (
	// RBAC状态
	RBACStatusActive   = "Active"
	RBACStatusInactive = "Inactive"
	RBACStatusError    = "Error"
)

const (
	// 角色类型
	RoleTypeBuiltIn = "BuiltIn"
	RoleTypeCustom  = "Custom"

	// 绑定类型
	BindingTypeNamespaced = "Namespaced"
	BindingTypeCluster    = "Cluster"
)

const (
	// 错误消息模板
	RoleNotFoundMessage               = "Role '%s' not found in namespace '%s'"
	ClusterRoleNotFoundMessage        = "ClusterRole '%s' not found"
	RoleBindingNotFoundMessage        = "RoleBinding '%s' not found in namespace '%s'"
	ClusterRoleBindingNotFoundMessage = "ClusterRoleBinding '%s' not found"
	ServiceAccountNotFoundMessage     = "ServiceAccount '%s' not found in namespace '%s'"

	RoleAlreadyExistsMessage               = "Role '%s' already exists in namespace '%s'"
	ClusterRoleAlreadyExistsMessage        = "ClusterRole '%s' already exists"
	RoleBindingAlreadyExistsMessage        = "RoleBinding '%s' already exists in namespace '%s'"
	ClusterRoleBindingAlreadyExistsMessage = "ClusterRoleBinding '%s' already exists"
	ServiceAccountAlreadyExistsMessage     = "ServiceAccount '%s' already exists in namespace '%s'"

	RoleInUseMessage               = "Role '%s' is still in use by one or more RoleBindings"
	ClusterRoleInUseMessage        = "ClusterRole '%s' is still in use by one or more bindings"
	ServiceAccountProtectedMessage = "ServiceAccount '%s' is protected and cannot be deleted"

	SystemProtectedResourceMessage = "Cannot delete system-protected resource '%s'"
)

const (
	// 默认分页参数
	DefaultPageNum  = 1
	DefaultPageSize = 10
	MaxPageSize     = 1000

	// 默认排序
	DefaultSortName  = ".metadata.name"
	DefaultSortOrder = "desc"
)

const (
	// 平台实体名称格式
	PlatformEntityNameFormat = "%s:%s:%s" // prefix:type:id
)
