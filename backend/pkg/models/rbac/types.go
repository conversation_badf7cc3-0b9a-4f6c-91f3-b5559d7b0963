package rbac

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	rbacv1 "k8s.io/api/rbac/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// ===== Role 相关结构体 =====

// Role 角色结构体，基于Kubernetes官方结构体
type Role struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Rules             []rbacv1.PolicyRule `json:"rules"`
}

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	ClusterID string              `json:"clusterId" binding:"required"`
	Namespace string              `json:"namespace" binding:"required"`
	Name      string              `json:"name" binding:"required"`
	Labels    map[string]string   `json:"labels,omitempty"`
	Rules     []rbacv1.PolicyRule `json:"rules" binding:"required"`
}

// CreateRoleResponse 创建角色响应
type CreateRoleResponse struct {
	metav1.ObjectMeta `json:"metadata"`
	Rules             []rbacv1.PolicyRule `json:"rules"`
}

// GetRoleRequest 获取角色请求
type GetRoleRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// GetRoleResponse 获取角色响应
type GetRoleResponse = Role

// ListRoleRequest 获取角色列表请求
type ListRoleRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace,omitempty"`
	// Filter 过滤器
	Filter resources.Filter[*Role] `json:"-"`
}

// ListRoleResponse 获取角色列表响应
type ListRoleResponse = models.PageableResponse[*Role]

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
	Role      *Role  `json:"role" binding:"required"`
}

// UpdateRoleResponse 更新角色响应
type UpdateRoleResponse struct {
	Name string `json:"name"`
}

// DeleteRoleRequest 删除角色请求
type DeleteRoleRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// DeleteRoleResponse 删除角色响应
type DeleteRoleResponse struct {
	Message string `json:"message"`
}

// ValidateRoleNameRequest 校验角色名称请求
type ValidateRoleNameRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// ValidateRoleNameResponse 校验角色名称响应
type ValidateRoleNameResponse struct {
	Message string `json:"message"`
}

// ===== ClusterRole 相关结构体 =====

// ClusterRole 集群角色结构体，基于Kubernetes官方结构体
type ClusterRole struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Rules             []rbacv1.PolicyRule     `json:"rules"`
	AggregationRule   *rbacv1.AggregationRule `json:"aggregationRule,omitempty"`
}

// CreateClusterRoleRequest 创建集群角色请求
type CreateClusterRoleRequest struct {
	ClusterID string              `json:"clusterId" binding:"required"`
	Name      string              `json:"name" binding:"required"`
	Labels    map[string]string   `json:"labels,omitempty"`
	Rules     []rbacv1.PolicyRule `json:"rules" binding:"required"`
}

// CreateClusterRoleResponse 创建集群角色响应
type CreateClusterRoleResponse struct {
	metav1.ObjectMeta `json:"metadata"`
	Rules             []rbacv1.PolicyRule `json:"rules"`
}

// GetClusterRoleRequest 获取集群角色请求
type GetClusterRoleRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// GetClusterRoleResponse 获取集群角色响应
type GetClusterRoleResponse = ClusterRole

// ListClusterRoleRequest 获取集群角色列表请求
type ListClusterRoleRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	// Filter 过滤器
	Filter resources.Filter[*ClusterRole] `json:"-"`
}

// ListClusterRoleResponse 获取集群角色列表响应
type ListClusterRoleResponse = models.PageableResponse[*ClusterRole]

// UpdateClusterRoleRequest 更新集群角色请求
type UpdateClusterRoleRequest struct {
	ClusterID   string       `json:"clusterId" binding:"required"`
	Name        string       `json:"name" binding:"required"`
	ClusterRole *ClusterRole `json:"clusterRole" binding:"required"`
}

// UpdateClusterRoleResponse 更新集群角色响应
type UpdateClusterRoleResponse struct {
	Name string `json:"name"`
}

// DeleteClusterRoleRequest 删除集群角色请求
type DeleteClusterRoleRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// DeleteClusterRoleResponse 删除集群角色响应
type DeleteClusterRoleResponse struct {
	Message string `json:"message"`
}

// ValidateClusterRoleNameRequest 校验集群角色名称请求
type ValidateClusterRoleNameRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// ValidateClusterRoleNameResponse 校验集群角色名称响应
type ValidateClusterRoleNameResponse struct {
	Message string `json:"message"`
}

// ===== Subject 业务模型 =====

// SubjectKind 主体类型枚举
type SubjectKind string

const (
	// 平台实体类型
	SubjectKindPlatformUser SubjectKind = "PlatformUser"
	SubjectKindPlatformRole SubjectKind = "PlatformRole"
	SubjectKindTenant       SubjectKind = "Tenant"
	SubjectKindProject      SubjectKind = "Project"
	// Kubernetes原生实体类型
	SubjectKindServiceAccount  SubjectKind = "ServiceAccount"
	SubjectKindKubernetesUser  SubjectKind = "KubernetesUser"
	SubjectKindKubernetesGroup SubjectKind = "KubernetesGroup"
)

// Subject 业务主体结构体（用于API输入输出）
type Subject struct {
	Kind      SubjectKind `json:"kind" binding:"required"`
	ID        string      `json:"id,omitempty"`            // 平台实体ID
	Name      string      `json:"name" binding:"required"` // 显示名称或K8s资源名
	Namespace string      `json:"namespace,omitempty"`     // ServiceAccount命名空间
}

// ===== API资源发现 =====

// GetAPIResourcesRequest 获取API资源请求
type GetAPIResourcesRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	APIGroup  string `json:"apiGroup,omitempty"`
	Keyword   string `json:"keyword,omitempty"`
}

// APIResourceGroup API资源组
type APIResourceGroup struct {
	Group     string        `json:"group"`
	Version   string        `json:"version"`
	Resources []APIResource `json:"resources"`
}

// APIResource API资源
type APIResource struct {
	Name       string `json:"name"`
	Namespaced bool   `json:"namespaced"`
	Kind       string `json:"kind"`
}

// GetAPIResourcesResponse 获取API资源响应
type GetAPIResourcesResponse struct {
	Verbs     []string           `json:"verbs"`
	Resources []APIResourceGroup `json:"resources"`
}

// ===== RoleBinding 相关结构体 =====

// RoleBinding 角色绑定结构体，基于Kubernetes官方结构体
type RoleBinding struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Subjects          []Subject      `json:"subjects"`
	RoleRef           rbacv1.RoleRef `json:"roleRef"`
}

// CreateRoleBindingRequest 创建角色绑定请求
type CreateRoleBindingRequest struct {
	ClusterID string    `json:"clusterId" binding:"required"`
	Namespace string    `json:"namespace" binding:"required"`
	Name      string    `json:"name" binding:"required"`
	RoleName  string    `json:"roleName" binding:"required"`
	RoleKind  string    `json:"roleKind" binding:"required"` // Role 或 ClusterRole
	Subjects  []Subject `json:"subjects" binding:"required"`
}

// CreateRoleBindingResponse 创建角色绑定响应
type CreateRoleBindingResponse struct {
	Name string `json:"name"`
}

// GetRoleBindingRequest 获取角色绑定请求
type GetRoleBindingRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// GetRoleBindingResponse 获取角色绑定响应
type GetRoleBindingResponse = RoleBinding

// ListRoleBindingRequest 获取角色绑定列表请求
type ListRoleBindingRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace" binding:"required"`
	// Filter 过滤器
	Filter resources.Filter[*RoleBinding] `json:"-"`
}

// ListRoleBindingResponse 获取角色绑定列表响应
type ListRoleBindingResponse = models.PageableResponse[*RoleBinding]

// UpdateRoleBindingRequest 更新角色绑定请求
type UpdateRoleBindingRequest struct {
	ClusterID string    `json:"clusterId" binding:"required"`
	Namespace string    `json:"namespace" binding:"required"`
	Name      string    `json:"name" binding:"required"`
	RoleName  string    `json:"roleName" binding:"required"`
	RoleKind  string    `json:"roleKind" binding:"required"`
	Subjects  []Subject `json:"subjects" binding:"required"`
}

// UpdateRoleBindingResponse 更新角色绑定响应
type UpdateRoleBindingResponse struct {
	Name string `json:"name"`
}

// DeleteRoleBindingRequest 删除角色绑定请求
type DeleteRoleBindingRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// DeleteRoleBindingResponse 删除角色绑定响应
type DeleteRoleBindingResponse struct {
	Message string `json:"message"`
}

// ===== ClusterRoleBinding 相关结构体 =====

// ClusterRoleBinding 集群角色绑定结构体，基于Kubernetes官方结构体
type ClusterRoleBinding struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Subjects          []Subject      `json:"subjects"`
	RoleRef           rbacv1.RoleRef `json:"roleRef"`
}

// CreateClusterRoleBindingRequest 创建集群角色绑定请求
type CreateClusterRoleBindingRequest struct {
	ClusterID string    `json:"clusterId" binding:"required"`
	Name      string    `json:"name" binding:"required"`
	RoleName  string    `json:"roleName" binding:"required"`
	Subjects  []Subject `json:"subjects" binding:"required"`
}

// CreateClusterRoleBindingResponse 创建集群角色绑定响应
type CreateClusterRoleBindingResponse struct {
	Name string `json:"name"`
}

// GetClusterRoleBindingRequest 获取集群角色绑定请求
type GetClusterRoleBindingRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// GetClusterRoleBindingResponse 获取集群角色绑定响应
type GetClusterRoleBindingResponse = ClusterRoleBinding

// ListClusterRoleBindingRequest 获取集群角色绑定列表请求
type ListClusterRoleBindingRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	// Filter 过滤器
	Filter resources.Filter[*ClusterRoleBinding] `json:"-"`
}

// ListClusterRoleBindingResponse 获取集群角色绑定列表响应
type ListClusterRoleBindingResponse = models.PageableResponse[*ClusterRoleBinding]

// UpdateClusterRoleBindingRequest 更新集群角色绑定请求
type UpdateClusterRoleBindingRequest struct {
	ClusterID string    `json:"clusterId" binding:"required"`
	Name      string    `json:"name" binding:"required"`
	RoleName  string    `json:"roleName" binding:"required"`
	Subjects  []Subject `json:"subjects" binding:"required"`
}

// UpdateClusterRoleBindingResponse 更新集群角色绑定响应
type UpdateClusterRoleBindingResponse struct {
	Name string `json:"name"`
}

// DeleteClusterRoleBindingRequest 删除集群角色绑定请求
type DeleteClusterRoleBindingRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// DeleteClusterRoleBindingResponse 删除集群角色绑定响应
type DeleteClusterRoleBindingResponse struct {
	Message string `json:"message"`
}

// ValidateClusterRoleBindingNameRequest 校验集群角色绑定名称请求
type ValidateClusterRoleBindingNameRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// ValidateClusterRoleBindingNameResponse 校验集群角色绑定名称响应
type ValidateClusterRoleBindingNameResponse struct {
	Message string `json:"message"`
}

// ===== ServiceAccount 相关结构体 =====

// ServiceAccount 服务账号结构体，基于Kubernetes官方结构体
type ServiceAccount struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Secrets           []ServiceAccountSecret `json:"secrets,omitempty"`
	Protected         bool                   `json:"protected"` // 是否受保护
}

// ServiceAccountSecret 服务账号密钥
type ServiceAccountSecret struct {
	Name string `json:"name"`
}

// CreateServiceAccountRequest 创建服务账号请求
type CreateServiceAccountRequest struct {
	ClusterID   string            `json:"clusterId" binding:"required"`
	Namespace   string            `json:"namespace" binding:"required"`
	Name        string            `json:"name" binding:"required"`
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

// CreateServiceAccountResponse 创建服务账号响应
type CreateServiceAccountResponse struct {
	metav1.ObjectMeta `json:"metadata"`
}

// GetServiceAccountRequest 获取服务账号请求
type GetServiceAccountRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// GetServiceAccountResponse 获取服务账号响应
type GetServiceAccountResponse = ServiceAccount

// ListServiceAccountRequest 获取服务账号列表请求
type ListServiceAccountRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace,omitempty"`
	// Filter 过滤器
	Filter resources.Filter[*ServiceAccount] `json:"-"`
}

// ListServiceAccountResponse 获取服务账号列表响应
type ListServiceAccountResponse = models.PageableResponse[*ServiceAccount]

// UpdateServiceAccountRequest 更新服务账号请求
type UpdateServiceAccountRequest struct {
	ClusterID   string            `json:"clusterId" binding:"required"`
	Namespace   string            `json:"namespace" binding:"required"`
	Name        string            `json:"name" binding:"required"`
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

// UpdateServiceAccountResponse 更新服务账号响应
type UpdateServiceAccountResponse struct {
	metav1.ObjectMeta `json:"metadata"`
}

// DeleteServiceAccountRequest 删除服务账号请求
type DeleteServiceAccountRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// DeleteServiceAccountResponse 删除服务账号响应
type DeleteServiceAccountResponse struct {
	Message string `json:"message"`
}

// ValidateServiceAccountNameRequest 校验服务账号名称请求
type ValidateServiceAccountNameRequest struct {
	ClusterID string `json:"clusterId" binding:"required"`
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// ValidateServiceAccountNameResponse 校验服务账号名称响应
type ValidateServiceAccountNameResponse struct {
	Valid  bool   `json:"valid"`
	Reason string `json:"reason,omitempty"`
}

// ===== 工作空间API相关结构体 =====

// WorkspaceRoleRequest 工作空间角色请求基础结构
type WorkspaceRoleRequest struct {
	OrganizationID string `json:"organizationId" binding:"required"`
	ProjectID      string `json:"projectId" binding:"required"`
	ClusterID      string `json:"clusterId" binding:"required"`
}

// WorkspaceListRoleRequest 工作空间获取角色列表请求
type WorkspaceListRoleRequest struct {
	WorkspaceRoleRequest
	Namespace string `json:"namespace,omitempty"`
	// Filter 过滤器
	Filter resources.Filter[*Role] `json:"-"`
}

// WorkspaceGetRoleRequest 工作空间获取角色请求
type WorkspaceGetRoleRequest struct {
	WorkspaceRoleRequest
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
}

// WorkspaceCreateRoleRequest 工作空间创建角色请求
type WorkspaceCreateRoleRequest struct {
	WorkspaceRoleRequest
	Namespace string              `json:"namespace" binding:"required"`
	Name      string              `json:"name" binding:"required"`
	Labels    map[string]string   `json:"labels,omitempty"`
	Rules     []rbacv1.PolicyRule `json:"rules" binding:"required"`
}

// WorkspaceUpdateRoleRequest 工作空间更新角色请求
type WorkspaceUpdateRoleRequest struct {
	WorkspaceRoleRequest
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
	Role      *Role  `json:"role" binding:"required"`
}

// WorkspaceDeleteRoleRequest 工作空间删除角色请求
type WorkspaceDeleteRoleRequest struct {
	WorkspaceRoleRequest
	Namespace string `json:"namespace" binding:"required"`
	Name      string `json:"name" binding:"required"`
}
