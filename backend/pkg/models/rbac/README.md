# RBAC模块实现说明

## 概述

本模块实现了基于Kubernetes原生RBAC的多租户权限控制系统，支持平台实体到K8s Subject的映射，提供了完整的角色、角色绑定、服务账号管理功能。

## 架构设计

### 1. 分层架构
```
Router Layer (路由层)
    ↓
Handler Layer (业务逻辑层)
    ↓
Models Layer (数据模型层)
    ↓
Kubernetes API (K8s集群)
```

### 2. 模块结构
```
backend/pkg/
├── models/rbac/
│   ├── types.go              # 数据模型定义
│   └── README.md            # 本文档
├── handler/rbac/
│   ├── interface.go         # 接口定义
│   ├── role.go             # Role处理器
│   ├── cluster_role.go     # ClusterRole处理器
│   ├── role_binding.go     # RoleBinding处理器
│   ├── cluster_role_binding.go # ClusterRoleBinding处理器
│   ├── service_account.go  # ServiceAccount处理器
│   ├── api_resource.go     # API资源发现
│   ├── workspace_role.go   # 工作空间角色
│   ├── helper.go           # 辅助函数
│   └── helper_test.go      # 单元测试
├── router/rbac/
│   ├── cluster_space.go    # 集群空间API路由
│   ├── cluster_space_bindings.go # 绑定相关路由
│   ├── cluster_space_sa.go # ServiceAccount路由
│   └── workspace.go        # 工作空间API路由
├── constants/
│   └── rbac.go             # RBAC常量定义
└── errors/
    └── constant.go         # 错误码定义（已扩展）
```

## 核心功能

### 1. 资源管理
- **Role**: 命名空间级别的角色管理
- **ClusterRole**: 集群级别的角色管理
- **RoleBinding**: 命名空间级别的角色绑定
- **ClusterRoleBinding**: 集群级别的角色绑定
- **ServiceAccount**: 服务账号管理

### 2. 平台实体映射
支持将平台实体映射为Kubernetes Subject：
- `PlatformUser` → K8s `User`
- `PlatformRole` → K8s `User`
- `Tenant` → K8s `Group`
- `Project` → K8s `Group`
- `ServiceAccount` → K8s `ServiceAccount`

### 3. API资源发现
提供集群API资源发现功能，用于权限配置界面。

### 4. 工作空间支持
支持基于组织和项目的工作空间角色管理。

## 数据模型

### Subject业务模型
```go
type Subject struct {
    Kind      SubjectKind `json:"kind"`      // 主体类型
    ID        string      `json:"id"`        // 平台实体ID
    Name      string      `json:"name"`      // 显示名称
    Namespace string      `json:"namespace"` // ServiceAccount命名空间
}
```

### 主体类型枚举
```go
const (
    // 平台实体类型
    SubjectKindPlatformUser  SubjectKind = "PlatformUser"
    SubjectKindPlatformRole  SubjectKind = "PlatformRole"
    SubjectKindTenant        SubjectKind = "Tenant"
    SubjectKindProject       SubjectKind = "Project"
    
    // Kubernetes原生实体类型
    SubjectKindServiceAccount  SubjectKind = "ServiceAccount"
    SubjectKindKubernetesUser  SubjectKind = "KubernetesUser"
    SubjectKindKubernetesGroup SubjectKind = "KubernetesGroup"
)
```

## API设计

### 1. 集群空间API
基础路径：`/api/v1/clusters/{clusterId}/rbac`

#### Role相关
- `POST /roles` - 创建角色
- `GET /roles/{name}` - 获取角色详情
- `GET /roles` - 获取角色列表
- `PUT /roles/{name}` - 更新角色
- `DELETE /roles/{name}` - 删除角色
- `POST /roles/validate-name` - 校验角色名称

#### ClusterRole相关
- `POST /cluster-roles` - 创建集群角色
- `GET /cluster-roles/{name}` - 获取集群角色详情
- `GET /cluster-roles` - 获取集群角色列表
- `PUT /cluster-roles/{name}` - 更新集群角色
- `DELETE /cluster-roles/{name}` - 删除集群角色
- `POST /cluster-roles/validate-name` - 校验集群角色名称

#### RoleBinding相关
- `POST /role-bindings` - 创建角色绑定
- `GET /role-bindings/{name}` - 获取角色绑定详情
- `GET /role-bindings` - 获取角色绑定列表
- `PUT /role-bindings/{name}` - 更新角色绑定
- `DELETE /role-bindings/{name}` - 删除角色绑定

#### ClusterRoleBinding相关
- `POST /cluster-role-bindings` - 创建集群角色绑定
- `GET /cluster-role-bindings/{name}` - 获取集群角色绑定详情
- `GET /cluster-role-bindings` - 获取集群角色绑定列表
- `PUT /cluster-role-bindings/{name}` - 更新集群角色绑定
- `DELETE /cluster-role-bindings/{name}` - 删除集群角色绑定
- `POST /cluster-role-bindings/validate-name` - 校验集群角色绑定名称

#### ServiceAccount相关
- `POST /service-accounts` - 创建服务账号
- `GET /service-accounts/{name}` - 获取服务账号详情
- `GET /service-accounts` - 获取服务账号列表
- `PUT /service-accounts/{name}` - 更新服务账号
- `DELETE /service-accounts/{name}` - 删除服务账号
- `POST /service-accounts/validate-name` - 校验服务账号名称

#### API资源发现
- `GET /api-resources` - 获取集群可用API资源

### 2. 工作空间API
基础路径：`/api/v1/organizations/{organizationId}/projects/{projectId}/rbac`

#### 工作空间Role相关
- `GET /roles` - 获取工作空间角色列表
- `GET /roles/{name}` - 获取工作空间角色详情
- `POST /roles` - 创建工作空间角色
- `PUT /roles/{name}` - 更新工作空间角色
- `DELETE /roles/{name}` - 删除工作空间角色

## 错误码

RBAC模块使用错误码范围：11000-11999

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| 11001 | 404 | 角色不存在 |
| 11002 | 409 | 角色已存在 |
| 11003 | 409 | 角色正在被使用，无法删除 |
| 11004 | 404 | 集群角色不存在 |
| 11005 | 409 | 集群角色已存在 |
| 11006 | 409 | 集群角色正在被使用，无法删除 |
| 11007 | 404 | 角色绑定不存在 |
| 11008 | 409 | 角色绑定已存在 |
| 11009 | 404 | 集群角色绑定不存在 |
| 11010 | 409 | 集群角色绑定已存在 |
| 11011 | 404 | 服务账号不存在 |
| 11012 | 409 | 服务账号已存在 |
| 11013 | 403 | 服务账号受保护，无法删除 |
| 11014 | 403 | 系统保护资源，无法删除 |
| 11015 | 400 | 无效的主体类型 |
| 11016 | 400 | 无效的角色引用 |
| 11017 | 404 | 集群不存在 |
| 11018 | 404 | 命名空间不存在 |
| 11019 | 400 | 无效的策略规则 |
| 11020 | 403 | 操作被禁止 |

## 安全特性

### 1. 资源保护
- 系统命名空间（kube-system、kube-public等）中的资源受保护
- 默认ServiceAccount受保护
- 带有系统保护标签的资源受保护

### 2. 依赖检查
- 删除Role前检查是否被RoleBinding使用
- 删除ClusterRole前检查是否被绑定使用

### 3. 权限验证
- 所有操作都需要有效的集群访问权限
- 支持命名空间级别的权限隔离

## 使用示例

### 1. 创建角色
```json
POST /api/v1/clusters/cluster-1/rbac/roles
{
  "name": "pod-reader",
  "namespace": "default",
  "rules": [
    {
      "apiGroups": [""],
      "resources": ["pods"],
      "verbs": ["get", "list", "watch"]
    }
  ]
}
```

### 2. 创建角色绑定
```json
POST /api/v1/clusters/cluster-1/namespaces/default/rbac/role-bindings
{
  "name": "pod-reader-binding",
  "roleName": "pod-reader",
  "roleKind": "Role",
  "subjects": [
    {
      "kind": "PlatformUser",
      "id": "user123",
      "name": "张三"
    },
    {
      "kind": "ServiceAccount",
      "name": "my-sa",
      "namespace": "default"
    }
  ]
}
```

## 测试

运行单元测试：
```bash
cd backend/pkg/handler/rbac
go test -v
```

测试覆盖了：
- Subject转换逻辑
- 资源保护检查
- RoleRef构建
- 辅助函数

## 扩展点

1. **平台实体集成**: 可以扩展Subject转换逻辑，集成平台用户、租户、项目服务
2. **权限模板**: 可以添加预定义的权限模板功能
3. **审计日志**: 可以添加RBAC操作的审计日志
4. **权限分析**: 可以添加权限分析和可视化功能
5. **批量操作**: 可以添加批量创建、更新、删除功能
