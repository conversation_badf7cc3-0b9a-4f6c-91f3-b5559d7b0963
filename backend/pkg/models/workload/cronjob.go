package workload

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	batchv1 "k8s.io/api/batch/v1"
)

// CronJob 相关结构体定义

// GetCronJobRequest ...
type GetCronJobRequest struct {
	common.ReadParam
}

// GetCronJobResponse ...
type GetCronJobResponse struct {
	CronJob batchv1.CronJob
}

// ListCronJobRequest ...
type ListCronJobRequest struct {
	common.ReadParam
}

// ListCronJobResponse ...
type ListCronJobResponse struct {
	models.PageableResponse[batchv1.CronJob]
}

// CreateCronJobRequest ...
type CreateCronJobRequest struct {
	common.WriteParam
}

// CreateCronJobResponse ...
type CreateCronJobResponse struct {
	CronJob batchv1.CronJob
}

// UpdateCronJobRequest ...
type UpdateCronJobRequest struct {
	common.WriteParam
}

// UpdateCronJobResponse ...
type UpdateCronJobResponse struct {
	CronJob batchv1.CronJob
}

// PatchCronJobRequest ...
type PatchCronJobRequest struct {
	common.WriteParam
}

// PatchCronJobResponse ...
type PatchCronJobResponse struct {
	CronJob batchv1.CronJob
}

// DeleteCronJobRequest ...
type DeleteCronJobRequest struct {
	common.WriteParam
}

// DeleteCronJobResponse ...
type DeleteCronJobResponse struct {
}

// DeleteAllCronJobRequest ...
type DeleteAllCronJobRequest struct {
	common.WriteParam
}

// DeleteAllCronJobResponse ...
type DeleteAllCronJobResponse struct {
}
