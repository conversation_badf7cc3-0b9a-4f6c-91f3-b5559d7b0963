package workload

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	appsv1 "k8s.io/api/apps/v1"
)

// DaemonSet 相关结构体定义

// GetDaemonSetRequest ...
type GetDaemonSetRequest struct {
	common.ReadParam
}

// GetDaemonSetResponse ...
type GetDaemonSetResponse struct {
	DaemonSet appsv1.DaemonSet
}

// ListDaemonSetRequest ...
type ListDaemonSetRequest struct {
	common.ReadParam
}

// ListDaemonSetResponse ...
type ListDaemonSetResponse struct {
	models.PageableResponse[appsv1.DaemonSet]
}

// CreateDaemonSetRequest ...
type CreateDaemonSetRequest struct {
	common.WriteParam
}

// CreateDaemonSetResponse ...
type CreateDaemonSetResponse struct {
	DaemonSet appsv1.DaemonSet
}

// UpdateDaemonSetRequest ...
type UpdateDaemonSetRequest struct {
	common.WriteParam
}

// UpdateDaemonSetResponse ...
type UpdateDaemonSetResponse struct {
	DaemonSet appsv1.DaemonSet
}

// PatchDaemonSetRequest ...
type PatchDaemonSetRequest struct {
	common.WriteParam
}

// PatchDaemonSetResponse ...
type PatchDaemonSetResponse struct {
	DaemonSet appsv1.DaemonSet
}

// DeleteDaemonSetRequest ...
type DeleteDaemonSetRequest struct {
	common.WriteParam
}

// DeleteDaemonSetResponse ...
type DeleteDaemonSetResponse struct {
}

// DeleteAllDaemonSetRequest ...
type DeleteAllDaemonSetRequest struct {
	common.WriteParam
}

// DeleteAllDaemonSetResponse ...
type DeleteAllDaemonSetResponse struct {
}
