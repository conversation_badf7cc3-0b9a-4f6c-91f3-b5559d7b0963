package workload

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	batchv1 "k8s.io/api/batch/v1"
)

// Job 相关结构体定义

// GetJobRequest ...
type GetJobRequest struct {
	common.ReadParam
}

// GetJobResponse ...
type GetJobResponse struct {
	Job batchv1.Job
}

// ListJobRequest ...
type ListJobRequest struct {
	common.ReadParam
}

// ListJobResponse ...
type ListJobResponse struct {
	models.PageableResponse[batchv1.Job]
}

// CreateJobRequest ...
type CreateJobRequest struct {
	common.WriteParam
}

// CreateJobResponse ...
type CreateJobResponse struct {
	Job batchv1.Job
}

// UpdateJobRequest ...
type UpdateJobRequest struct {
	common.WriteParam
}

// UpdateJobResponse ...
type UpdateJobResponse struct {
	Job batchv1.Job
}

// PatchJobRequest ...
type PatchJobRequest struct {
	common.WriteParam
}

// PatchJobResponse ...
type PatchJobResponse struct {
	Job batchv1.Job
}

// DeleteJobRequest ...
type DeleteJobRequest struct {
	common.WriteParam
}

// DeleteJobResponse ...
type DeleteJobResponse struct {
}

// DeleteAllJobRequest ...
type DeleteAllJobRequest struct {
	common.WriteParam
}

// DeleteAllJobResponse ...
type DeleteAllJobResponse struct {
}
