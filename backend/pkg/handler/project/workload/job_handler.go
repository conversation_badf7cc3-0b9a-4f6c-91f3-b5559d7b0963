package workload

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	workloadmodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/workload"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
	batchv1 "k8s.io/api/batch/v1"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

var _ JobHandler = (*internalJobHandler)(nil)

// NewJobHandler ...
func NewJobHandler() JobHandler {
	return &internalJobHandler{
		CommonHandler: common.NewCommonHandler(),
		Converter:     newWorkloadUnstructuredConverter[batchv1.Job](),
	}
}

type internalJobHandler struct {
	CommonHandler common.CommonIntf
	Converter     *internalUnstructuredConverter[batchv1.Job]
}

func (i *internalJobHandler) FromUnstructured(object ctrlclient.Object) (*batchv1.Job, error) {
	return i.Converter.FromUnstructured(object)
}

func (i *internalJobHandler) Get(ctx context.Context, request *workloadmodels.GetJobRequest) (response *workloadmodels.GetJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbGet); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Get(ctx, request)
	if err != nil {
		return nil, err
	}
	job, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	return &workloadmodels.GetJobResponse{Job: *job}, nil
}

func (i *internalJobHandler) List(ctx context.Context, request *workloadmodels.ListJobRequest) (response *workloadmodels.ListJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbList); err != nil {
		return nil, err
	}
	list, err := i.CommonHandler.List(ctx, request)
	if err != nil {
		return nil, err
	}
	filter, err := request.Filter.FilterResult(list.Items)
	if err != nil {
		return nil, err
	}
	var jobs []batchv1.Job
	for _, item := range filter.Items {
		job, err := i.FromUnstructured(item.DeepCopy())
		if err != nil {
			return nil, err
		}
		jobs = append(jobs, *job)
	}
	response = new(workloadmodels.ListJobResponse)
	response.PageableResponse = models.PageableResponse[batchv1.Job]{
		Items:      jobs,
		TotalCount: filter.TotalCount,
	}
	return response, nil
}

func (i *internalJobHandler) Create(ctx context.Context, request *workloadmodels.CreateJobRequest) (response *workloadmodels.CreateJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbCreate); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Create(ctx, request)
	if err != nil {
		return nil, err
	}
	job, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.CreateJobResponse)
	response.Job = *job
	return response, nil
}

func (i *internalJobHandler) Update(ctx context.Context, request *workloadmodels.UpdateJobRequest) (response *workloadmodels.UpdateJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbUpdate); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Update(ctx, request)
	if err != nil {
		return nil, err
	}
	job, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.UpdateJobResponse)
	response.Job = *job
	return response, nil
}

func (i *internalJobHandler) Patch(ctx context.Context, request *workloadmodels.PatchJobRequest) (response *workloadmodels.PatchJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbPatch); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Patch(ctx, request)
	if err != nil {
		return nil, err
	}
	job, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.PatchJobResponse)
	response.Job = *job
	return response, nil
}

func (i *internalJobHandler) Delete(ctx context.Context, request *workloadmodels.DeleteJobRequest) (response *workloadmodels.DeleteJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbDelete); err != nil {
		return nil, err
	}
	err = i.CommonHandler.Delete(ctx, request)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.DeleteJobResponse)
	return response, nil
}

func (i *internalJobHandler) DeleteAll(ctx context.Context, request *workloadmodels.DeleteAllJobRequest) (response *workloadmodels.DeleteAllJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbDeleteCollection); err != nil {
		return nil, err
	}
	err = i.CommonHandler.DeleteAll(ctx, request)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.DeleteAllJobResponse)
	return response, nil
}
