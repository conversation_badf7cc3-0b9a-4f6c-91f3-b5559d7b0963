package workload

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	workloadmodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/workload"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
	batchv1 "k8s.io/api/batch/v1"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

var _ CronJobHandler = (*internalCronJobHandler)(nil)

// NewCronJobHandler ...
func NewCronJobHandler() CronJobHandler {
	return &internalCronJobHandler{
		CommonHandler: common.NewCommonHandler(),
		Converter:     newWorkloadUnstructuredConverter[batchv1.CronJob](),
	}
}

type internalCronJobHandler struct {
	CommonHandler common.CommonIntf
	Converter     *internalUnstructuredConverter[batchv1.CronJob]
}

func (i *internalCronJobHandler) FromUnstructured(object ctrlclient.Object) (*batchv1.CronJob, error) {
	return i.Converter.FromUnstructured(object)
}

func (i *internalCronJobHandler) Get(ctx context.Context, request *workloadmodels.GetCronJobRequest) (response *workloadmodels.GetCronJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbGet); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Get(ctx, request)
	if err != nil {
		return nil, err
	}
	cronjob, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	return &workloadmodels.GetCronJobResponse{CronJob: *cronjob}, nil
}

func (i *internalCronJobHandler) List(ctx context.Context, request *workloadmodels.ListCronJobRequest) (response *workloadmodels.ListCronJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbList); err != nil {
		return nil, err
	}
	list, err := i.CommonHandler.List(ctx, request)
	if err != nil {
		return nil, err
	}
	filter, err := request.Filter.FilterResult(list.Items)
	if err != nil {
		return nil, err
	}
	var cronjobs []batchv1.CronJob
	for _, item := range filter.Items {
		cronjob, err := i.FromUnstructured(item.DeepCopy())
		if err != nil {
			return nil, err
		}
		cronjobs = append(cronjobs, *cronjob)
	}
	response = new(workloadmodels.ListCronJobResponse)
	response.PageableResponse = models.PageableResponse[batchv1.CronJob]{
		Items:      cronjobs,
		TotalCount: filter.TotalCount,
	}
	return response, nil
}

func (i *internalCronJobHandler) Create(ctx context.Context, request *workloadmodels.CreateCronJobRequest) (response *workloadmodels.CreateCronJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbCreate); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Create(ctx, request)
	if err != nil {
		return nil, err
	}
	cronjob, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.CreateCronJobResponse)
	response.CronJob = *cronjob
	return response, nil
}

func (i *internalCronJobHandler) Update(ctx context.Context, request *workloadmodels.UpdateCronJobRequest) (response *workloadmodels.UpdateCronJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbUpdate); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Update(ctx, request)
	if err != nil {
		return nil, err
	}
	cronjob, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.UpdateCronJobResponse)
	response.CronJob = *cronjob
	return response, nil
}

func (i *internalCronJobHandler) Patch(ctx context.Context, request *workloadmodels.PatchCronJobRequest) (response *workloadmodels.PatchCronJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbPatch); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Patch(ctx, request)
	if err != nil {
		return nil, err
	}
	cronjob, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.PatchCronJobResponse)
	response.CronJob = *cronjob
	return response, nil
}

func (i *internalCronJobHandler) Delete(ctx context.Context, request *workloadmodels.DeleteCronJobRequest) (response *workloadmodels.DeleteCronJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbDelete); err != nil {
		return nil, err
	}
	err = i.CommonHandler.Delete(ctx, request)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.DeleteCronJobResponse)
	return response, nil
}

func (i *internalCronJobHandler) DeleteAll(ctx context.Context, request *workloadmodels.DeleteAllCronJobRequest) (response *workloadmodels.DeleteAllCronJobResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbDeleteCollection); err != nil {
		return nil, err
	}
	err = i.CommonHandler.DeleteAll(ctx, request)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.DeleteAllCronJobResponse)
	return response, nil
}
