package workload

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	workloadmodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/workload"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
	appsv1 "k8s.io/api/apps/v1"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

var _ DaemonSetHandler = (*internalDaemonSetHandler)(nil)

type internalDaemonSetHandler struct {
	CommonHandler common.CommonIntf
	Converter     *internalUnstructuredConverter[appsv1.DaemonSet]
}

func NewDaemonSetHandler() DaemonSetHandler {
	return &internalDaemonSetHandler{
		CommonHandler: common.NewCommonHandler(),
		Converter:     newWorkloadUnstructuredConverter[appsv1.DaemonSet](),
	}
}

func (i *internalDaemonSetHandler) FromUnstructured(object ctrlclient.Object) (*appsv1.DaemonSet, error) {
	return i.Converter.FromUnstructured(object)
}

func (i *internalDaemonSetHandler) Get(ctx context.Context, request *workloadmodels.GetDaemonSetRequest) (response *workloadmodels.GetDaemonSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbGet); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Get(ctx, request)
	if err != nil {
		return nil, err
	}
	ds, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	return &workloadmodels.GetDaemonSetResponse{DaemonSet: *ds}, nil
}

func (i *internalDaemonSetHandler) List(ctx context.Context, request *workloadmodels.ListDaemonSetRequest) (response *workloadmodels.ListDaemonSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbList); err != nil {
		return nil, err
	}
	list, err := i.CommonHandler.List(ctx, request)
	if err != nil {
		return nil, err
	}
	filter, err := request.Filter.FilterResult(list.Items)
	if err != nil {
		return nil, err
	}
	var daemonsets []appsv1.DaemonSet
	for _, item := range filter.Items {
		ds, err := i.FromUnstructured(item.DeepCopy())
		if err != nil {
			return nil, err
		}
		daemonsets = append(daemonsets, *ds)
	}
	response = new(workloadmodels.ListDaemonSetResponse)
	response.PageableResponse = models.PageableResponse[appsv1.DaemonSet]{
		Items:      daemonsets,
		TotalCount: filter.TotalCount,
	}
	return response, nil
}

func (i *internalDaemonSetHandler) Create(ctx context.Context, request *workloadmodels.CreateDaemonSetRequest) (response *workloadmodels.CreateDaemonSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbCreate); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Create(ctx, request)
	if err != nil {
		return nil, err
	}
	ds, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.CreateDaemonSetResponse)
	response.DaemonSet = *ds
	return response, nil
}

func (i *internalDaemonSetHandler) Update(ctx context.Context, request *workloadmodels.UpdateDaemonSetRequest) (response *workloadmodels.UpdateDaemonSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbUpdate); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Update(ctx, request)
	if err != nil {
		return nil, err
	}
	ds, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.UpdateDaemonSetResponse)
	response.DaemonSet = *ds
	return response, nil
}

func (i *internalDaemonSetHandler) Patch(ctx context.Context, request *workloadmodels.PatchDaemonSetRequest) (response *workloadmodels.PatchDaemonSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbPatch); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Patch(ctx, request)
	if err != nil {
		return nil, err
	}
	ds, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.PatchDaemonSetResponse)
	response.DaemonSet = *ds
	return response, nil
}

func (i *internalDaemonSetHandler) Delete(ctx context.Context, request *workloadmodels.DeleteDaemonSetRequest) (response *workloadmodels.DeleteDaemonSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbDelete); err != nil {
		return nil, err
	}
	err = i.CommonHandler.Delete(ctx, request)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.DeleteDaemonSetResponse)
	return response, nil
}

func (i *internalDaemonSetHandler) DeleteAll(ctx context.Context, request *workloadmodels.DeleteAllDaemonSetRequest) (response *workloadmodels.DeleteAllDaemonSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbDeleteCollection); err != nil {
		return nil, err
	}
	err = i.CommonHandler.DeleteAll(ctx, request)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.DeleteAllDaemonSetResponse)
	return response, nil
}
