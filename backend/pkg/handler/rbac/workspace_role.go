package rbac

import (
	"context"

	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
)

// ListWorkspaceRole 获取工作空间角色列表
func (h *handler) ListWorkspaceRole(ctx context.Context, req *rbac.WorkspaceListRoleRequest) (resp *rbac.ListRoleResponse, err error) {
	logger.GetLogger().Info("Listing workspace roles",
		zap.String("organizationId", req.OrganizationID),
		zap.String("projectId", req.ProjectID),
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
	)

	// 转换为标准的ListRoleRequest
	listReq := &rbac.ListRoleRequest{
		ClusterID: req.ClusterID,
		Namespace: req.Namespace,
		Filter:    req.Filter,
	}

	// 调用标准的ListRole方法
	return h.ListRole(ctx, listReq)
}

// GetWorkspaceRole 获取工作空间角色详情
func (h *handler) GetWorkspaceRole(ctx context.Context, req *rbac.WorkspaceGetRoleRequest) (resp *rbac.GetRoleResponse, err error) {
	logger.GetLogger().Info("Getting workspace role",
		zap.String("organizationId", req.OrganizationID),
		zap.String("projectId", req.ProjectID),
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	// 转换为标准的GetRoleRequest
	getRoleReq := &rbac.GetRoleRequest{
		ClusterID: req.ClusterID,
		Namespace: req.Namespace,
		Name:      req.Name,
	}

	// 调用标准的GetRole方法
	return h.GetRole(ctx, getRoleReq)
}

// CreateWorkspaceRole 创建工作空间角色
func (h *handler) CreateWorkspaceRole(ctx context.Context, req *rbac.WorkspaceCreateRoleRequest) (resp *rbac.CreateRoleResponse, err error) {
	logger.GetLogger().Info("Creating workspace role",
		zap.String("organizationId", req.OrganizationID),
		zap.String("projectId", req.ProjectID),
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	// 转换为标准的CreateRoleRequest
	createReq := &rbac.CreateRoleRequest{
		ClusterID: req.ClusterID,
		Namespace: req.Namespace,
		Name:      req.Name,
		Labels:    req.Labels,
		Rules:     req.Rules,
	}

	// 添加工作空间标签
	if createReq.Labels == nil {
		createReq.Labels = make(map[string]string)
	}
	createReq.Labels["workspace.olympus.io/organization-id"] = req.OrganizationID
	createReq.Labels["workspace.olympus.io/project-id"] = req.ProjectID

	// 调用标准的CreateRole方法
	return h.CreateRole(ctx, createReq)
}

// UpdateWorkspaceRole 更新工作空间角色
func (h *handler) UpdateWorkspaceRole(ctx context.Context, req *rbac.WorkspaceUpdateRoleRequest) (resp *rbac.UpdateRoleResponse, err error) {
	logger.GetLogger().Info("Updating workspace role",
		zap.String("organizationId", req.OrganizationID),
		zap.String("projectId", req.ProjectID),
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	// 确保工作空间标签存在
	if req.Role.Labels == nil {
		req.Role.Labels = make(map[string]string)
	}
	req.Role.Labels["workspace.olympus.io/organization-id"] = req.OrganizationID
	req.Role.Labels["workspace.olympus.io/project-id"] = req.ProjectID

	// 转换为标准的UpdateRoleRequest
	updateReq := &rbac.UpdateRoleRequest{
		ClusterID: req.ClusterID,
		Namespace: req.Namespace,
		Name:      req.Name,
		Role:      req.Role,
	}

	// 调用标准的UpdateRole方法
	return h.UpdateRole(ctx, updateReq)
}

// DeleteWorkspaceRole 删除工作空间角色
func (h *handler) DeleteWorkspaceRole(ctx context.Context, req *rbac.WorkspaceDeleteRoleRequest) (resp *rbac.DeleteRoleResponse, err error) {
	logger.GetLogger().Info("Deleting workspace role",
		zap.String("organizationId", req.OrganizationID),
		zap.String("projectId", req.ProjectID),
		zap.String("cluster", req.ClusterID),
		zap.String("namespace", req.Namespace),
		zap.String("roleName", req.Name),
	)

	// 转换为标准的DeleteRoleRequest
	deleteReq := &rbac.DeleteRoleRequest{
		ClusterID: req.ClusterID,
		Namespace: req.Namespace,
		Name:      req.Name,
	}

	// 调用标准的DeleteRole方法
	return h.DeleteRole(ctx, deleteReq)
}
