package rbac

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/rbac"
	rbacv1 "k8s.io/api/rbac/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// CreateClusterRoleBinding 创建集群角色绑定
func (h *handler) CreateClusterRoleBinding(ctx context.Context, req *rbac.CreateClusterRoleBindingRequest) (resp *rbac.CreateClusterRoleBindingResponse, err error) {
	logger.GetLogger().Info("Creating cluster role binding",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleBindingName", req.Name),
		zap.String("roleName", req.RoleName),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 转换业务Subject为Kubernetes Subject
	k8sSubjects, err := convertBusinessSubjectsToK8s(req.Subjects)
	if err != nil {
		return nil, err
	}

	// 构建ClusterRoleBinding对象
	clusterRoleBinding := &rbacv1.ClusterRoleBinding{
		TypeMeta: metav1.TypeMeta{
			APIVersion: constants.RBACAPIGroup + "/" + constants.RBACAPIVersion,
			Kind:       constants.ClusterRoleBindingKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: req.Name,
		},
		Subjects: k8sSubjects,
		RoleRef:  buildRoleRef(req.RoleName, constants.ClusterRoleKind),
	}

	// 创建ClusterRoleBinding
	if err := k8sClient.Create(ctx, clusterRoleBinding); err != nil {
		if apierrors.IsAlreadyExists(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleBindingAlreadyExists, req.Name)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	logger.GetLogger().Info("ClusterRoleBinding created successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleBindingName", req.Name),
	)

	return &rbac.CreateClusterRoleBindingResponse{
		Name: req.Name,
	}, nil
}

// GetClusterRoleBinding 获取集群角色绑定详情
func (h *handler) GetClusterRoleBinding(ctx context.Context, req *rbac.GetClusterRoleBindingRequest) (resp *rbac.GetClusterRoleBindingResponse, err error) {
	logger.GetLogger().Info("Getting cluster role binding",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleBindingName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取ClusterRoleBinding
	clusterRoleBinding := &rbacv1.ClusterRoleBinding{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Name: req.Name,
	}, clusterRoleBinding); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleBindingNotFound, req.Name)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 转换Kubernetes Subject为业务Subject
	businessSubjects := convertK8sSubjectsToBusiness(clusterRoleBinding.Subjects)

	return &rbac.ClusterRoleBinding{
		TypeMeta:   clusterRoleBinding.TypeMeta,
		ObjectMeta: clusterRoleBinding.ObjectMeta,
		Subjects:   businessSubjects,
		RoleRef:    clusterRoleBinding.RoleRef,
	}, nil
}

// ListClusterRoleBinding 获取集群角色绑定列表
func (h *handler) ListClusterRoleBinding(ctx context.Context, req *rbac.ListClusterRoleBindingRequest) (resp *rbac.ListClusterRoleBindingResponse, err error) {
	logger.GetLogger().Info("Listing cluster role bindings",
		zap.String("cluster", req.ClusterID),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取ClusterRoleBinding列表
	clusterRoleBindingList := &rbacv1.ClusterRoleBindingList{}
	if err := k8sClient.List(ctx, clusterRoleBindingList); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	// 转换为业务模型
	var items []*rbac.ClusterRoleBinding
	for _, crb := range clusterRoleBindingList.Items {
		businessSubjects := convertK8sSubjectsToBusiness(crb.Subjects)
		items = append(items, &rbac.ClusterRoleBinding{
			TypeMeta:   crb.TypeMeta,
			ObjectMeta: crb.ObjectMeta,
			Subjects:   businessSubjects,
			RoleRef:    crb.RoleRef,
		})
	}

	// 使用Filter过滤器过滤集群角色绑定列表
	result, err := req.Filter.Filter(items)
	if err != nil {
		return nil, err
	}

	logger.GetLogger().Info("ClusterRoleBindings listed successfully",
		zap.String("cluster", req.ClusterID),
		zap.Int("total", result.Total),
	)

	return result, nil
}

// UpdateClusterRoleBinding 更新集群角色绑定
func (h *handler) UpdateClusterRoleBinding(ctx context.Context, req *rbac.UpdateClusterRoleBindingRequest) (resp *rbac.UpdateClusterRoleBindingResponse, err error) {
	logger.GetLogger().Info("Updating cluster role binding",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleBindingName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 转换业务Subject为Kubernetes Subject
	k8sSubjects, err := convertBusinessSubjectsToK8s(req.Subjects)
	if err != nil {
		return nil, err
	}

	// 获取现有ClusterRoleBinding
	existingClusterRoleBinding := &rbacv1.ClusterRoleBinding{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Name: req.Name,
	}, existingClusterRoleBinding); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleBindingNotFound, req.Name)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 更新ClusterRoleBinding
	existingClusterRoleBinding.Subjects = k8sSubjects
	existingClusterRoleBinding.RoleRef = buildRoleRef(req.RoleName, constants.ClusterRoleKind)

	if err := k8sClient.Update(ctx, existingClusterRoleBinding); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	logger.GetLogger().Info("ClusterRoleBinding updated successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleBindingName", req.Name),
	)

	return &rbac.UpdateClusterRoleBindingResponse{
		Name: req.Name,
	}, nil
}

// DeleteClusterRoleBinding 删除集群角色绑定
func (h *handler) DeleteClusterRoleBinding(ctx context.Context, req *rbac.DeleteClusterRoleBindingRequest) (resp *rbac.DeleteClusterRoleBindingResponse, err error) {
	logger.GetLogger().Info("Deleting cluster role binding",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleBindingName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 获取ClusterRoleBinding
	clusterRoleBinding := &rbacv1.ClusterRoleBinding{}
	if err := k8sClient.Get(ctx, client.ObjectKey{
		Name: req.Name,
	}, clusterRoleBinding); err != nil {
		if apierrors.IsNotFound(err) {
			return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleBindingNotFound, req.Name)
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 删除ClusterRoleBinding
	if err := k8sClient.Delete(ctx, clusterRoleBinding); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	logger.GetLogger().Info("ClusterRoleBinding deleted successfully",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleBindingName", req.Name),
	)

	return &rbac.DeleteClusterRoleBindingResponse{
		Message: fmt.Sprintf("ClusterRoleBinding '%s' deleted successfully", req.Name),
	}, nil
}

// ValidateClusterRoleBindingName 校验集群角色绑定名称
func (h *handler) ValidateClusterRoleBindingName(ctx context.Context, req *rbac.ValidateClusterRoleBindingNameRequest) (resp *rbac.ValidateClusterRoleBindingNameResponse, err error) {
	logger.GetLogger().Info("Validating cluster role binding name",
		zap.String("cluster", req.ClusterID),
		zap.String("clusterRoleBindingName", req.Name),
	)

	// 获取集群客户端
	k8sClient, err := getClusterClient(req.ClusterID)
	if err != nil {
		return nil, err
	}

	// 检查ClusterRoleBinding是否存在
	clusterRoleBinding := &rbacv1.ClusterRoleBinding{}
	err = k8sClient.Get(ctx, client.ObjectKey{
		Name: req.Name,
	}, clusterRoleBinding)

	if err != nil {
		if apierrors.IsNotFound(err) {
			// 名称可用
			return &rbac.ValidateClusterRoleBindingNameResponse{
				Message: fmt.Sprintf("ClusterRoleBinding name '%s' is available", req.Name),
			}, nil
		}
		return nil, errors.NewFromError(ctx, err)
	}

	// 名称已存在
	return nil, errors.NewFromCodeFormatMessage(errors.Var.RBACClusterRoleBindingAlreadyExists, req.Name)
}
