package errors

import (
	"net/http"
)

var Var = errorCodeVar{

	// Kubernetes Error
	K8sUnauthorized:                newErrorCode(http.StatusInternalServerError, 8401, "权限未授权"),      // metav1.StatusReasonUnauthorized
	K8sForbidden:                   newErrorCode(http.StatusInternalServerError, 8402, "无权操作"),       // metav1.StatusReasonForbidden
	K8sNotFound:                    newErrorCode(http.StatusInternalServerError, 8403, "资源未找到"),      // metav1.StatusReasonNotFound
	K8sAlreadyExists:               newErrorCode(http.StatusInternalServerError, 8404, "资源已存在"),      // metav1.StatusReasonAlreadyExists
	K8sConflict:                    newErrorCode(http.StatusInternalServerError, 8405, "资源冲突"),       // metav1.StatusReasonConflict
	K8sGone:                        newErrorCode(http.StatusInternalServerError, 8406, "资源已被移除"),     // metav1.StatusReasonGone
	K8sInvalid:                     newErrorCode(http.StatusInternalServerError, 8407, "请求无效"),       // metav1.StatusReasonInvalid
	K8sServerTimeout:               newErrorCode(http.StatusInternalServerError, 8408, "服务器超时"),      // metav1.StatusReasonServerTimeout
	K8sTimeout:                     newErrorCode(http.StatusInternalServerError, 8409, "操作超时"),       // metav1.StatusReasonTimeout
	K8sTooManyRequests:             newErrorCode(http.StatusInternalServerError, 8410, "请求过多"),       // metav1.StatusReasonTooManyRequests
	K8sBadRequest:                  newErrorCode(http.StatusInternalServerError, 8411, "错误请求"),       // metav1.StatusReasonBadRequest
	K8sMethodNotAllowed:            newErrorCode(http.StatusInternalServerError, 8412, "请求方法不允许"),    // metav1.StatusReasonMethodNotAllowed
	K8sNotAcceptable:               newErrorCode(http.StatusInternalServerError, 8413, "请求不可接受"),     // metav1.StatusReasonNotAcceptable
	K8sRequestEntityTooLarge:       newErrorCode(http.StatusInternalServerError, 8414, "请求实体过大"),     // metav1.StatusReasonRequestEntityTooLarge
	K8sUnsupportedMediaType:        newErrorCode(http.StatusInternalServerError, 8415, "不支持的媒体类型"),   // metav1.StatusReasonUnsupportedMediaType
	K8sInternalError:               newErrorCode(http.StatusInternalServerError, 8416, "k8s服务器内部错误"), // metav1.StatusReasonInternalError
	K8sExpired:                     newErrorCode(http.StatusInternalServerError, 8417, "资源已过期"),      // metav1.StatusReasonExpired
	K8sServiceUnavailable:          newErrorCode(http.StatusInternalServerError, 8418, "服务不可用"),      // metav1.StatusReasonServiceUnavailable
	K8sError:                       newErrorCode(http.StatusInternalServerError, 8419, "k8s错误"),      // metav1.StatusReasonInternalError
	K8sIsDeleting:                  newErrorCode(http.StatusInternalServerError, 8420, "k8s资源正在删除中"),
	K8sForbiddenDeletedByFinalizer: newErrorCode(http.StatusInternalServerError, 8421, "此资源被Finalizers(终结器)保护，暂不允许删除"),

	UnKnow:                 newErrorCode(http.StatusInternalServerError, 10000, "未知的错误"),
	ResourceNotFount:       newErrorCode(http.StatusNotFound, 10001, "资源不存在"),
	AuthForbidden:          newErrorCode(http.StatusForbidden, 10002, "请求被拒绝"),
	InValidToken:           newErrorCode(http.StatusUnauthorized, 10003, "无效的token"),
	ClusterNotExist:        newErrorCode(http.StatusInternalServerError, 10004, "集群不存在"),
	ClusterStatusNotOnline: newErrorCode(http.StatusInternalServerError, 10005, "集群未在线"),
	ParamError:             newErrorCode(http.StatusInternalServerError, 10006, "参数错误"),
	ConnectFailure:         newErrorCode(http.StatusInternalServerError, 10007, "连接失败"),
	BoolParamError:         newErrorCode(http.StatusInternalServerError, 10008, "请输入布尔类型值，参考值true|false"),

	// IllegalUserStatus
	// 用户相关
	IllegalUserStatus: newErrorCode(http.StatusInternalServerError, 10010, "未知的用户状态"),
	UserUnLogin:       newErrorCode(http.StatusUnauthorized, 10011, "用户未登录"),
	IllegalUserStruct: newErrorCode(http.StatusInternalServerError, 10012, "无法解析的用户结构"),
	TokenEffective:    newErrorCode(http.StatusForbidden, 10013, "token 未生效"),
	TokenExpire:       newErrorCode(http.StatusForbidden, 10014, "token 已过期"),

	// CloudServiceListResourcesError  resource list error
	CloudServiceListResourcesError: newErrorCode(http.StatusInternalServerError, 10020, "获取云服务资源列表失败"),

	// AsyncJobForcedStop
	// AsyncJobTimeout
	// AsyncError
	// 异步任务相关
	AsyncJobForcedStop: newErrorCode(http.StatusInternalServerError, 10050, "异步任务被强制停止"),
	AsyncJobTimeout:    newErrorCode(http.StatusInternalServerError, 10051, "异步任务执行超时"),
	AsyncError:         newErrorCode(http.StatusInternalServerError, 10052, "异步任务执行失败"),

	// StorageServerNameRepeat
	// StorageServerUsages
	// DownloadURLTimeout
	// DownloadURLError
	// SchedulesNameRepeat
	// RestoresTemplateNameRepeat
	// SchedulesIsUsed
	// RestoreTemplateNotFound
	// velero 相关参数
	StorageServerNameRepeat:    newErrorCode(http.StatusInternalServerError, 10100, "备份仓库名称重复"),
	StorageServerUsages:        newErrorCode(http.StatusInternalServerError, 10101, "当前备份仓库已被备份策略绑定，无法删除"),
	DownloadURLTimeout:         newErrorCode(http.StatusRequestTimeout, 10102, "请求超时,请检查备份仓库中备份文件是否存在"),
	DownloadURLError:           newErrorCode(http.StatusInternalServerError, 10103, "请求失败"),
	SchedulesNameRepeat:        newErrorCode(http.StatusInternalServerError, 10104, "备份策略名称重复"),
	RestoresTemplateNameRepeat: newErrorCode(http.StatusInternalServerError, 10105, "还原策略名称重复"),
	SchedulesIsUsed:            newErrorCode(http.StatusInternalServerError, 10106, "备份策略已被使用"),
	RestoreTemplateNotFound:    newErrorCode(http.StatusInternalServerError, 10107, "还原策略不存在"),
	StorageServerNotExist:      newErrorCode(http.StatusInternalServerError, 10108, "存储服务不存在"),

	// ClusterManaged
	// ClusterCreating
	// 集群相关
	ClusterNameRepeated:                         newErrorCode(http.StatusInternalServerError, 10201, "集群名称已存在，请修改集群名称"),
	IllegalNodeUsername:                         newErrorCode(http.StatusInternalServerError, 10202, "节点用户名不能为空"),
	IllegalNodePassword:                         newErrorCode(http.StatusInternalServerError, 10203, "节点密码不能为空"),
	SolutionNameIsNotSet:                        newErrorCode(http.StatusInternalServerError, 10204, "solution 名称未设置"),
	UnKnowAuthType:                              newErrorCode(http.StatusInternalServerError, 10205, "未知的节点认证类型"),
	InstallerLabelKeyEmpty:                      newErrorCode(http.StatusInternalServerError, 10206, "installer 不包含label的值"),
	ClusterInstallerNumberError:                 newErrorCode(http.StatusInternalServerError, 10207, "系统错误，请联系管理员，后台存在同名的集群创建任务"),
	ClusterCreateTaskNotExist:                   newErrorCode(http.StatusInternalServerError, 10208, "创建集群任务不存在"),
	ClusterCouldNotDeleteByStatus:               newErrorCode(http.StatusInternalServerError, 10209, "不可删除非执行失败的创建集群任务"),
	StellarisStandbyValueNotNullOnActiveStandBy: newErrorCode(http.StatusInternalServerError, 10210, "主备切换模式下，备管理集群Stellaris组件访问地址信息必填"),
	ChooseOneContainerNetworkSolutionAtLest:     newErrorCode(http.StatusInternalServerError, 10211, "请至少选择一种容器网络解决方案"),
	ApiServerAddressRequiredInHAModel:           newErrorCode(http.StatusInternalServerError, 10212, "高可用模式下集群地址必填"),
	DefaultLBAddressRequiredInHAModel:           newErrorCode(http.StatusInternalServerError, 10213, "高可用模式下集群默认负载均衡"),
	StellariesSecretLost:                        newErrorCode(http.StatusInternalServerError, 10214, "stellaries token 配置的secret 文件丢失，请联系管理员"),
	StellariesSecretKeyLost:                     newErrorCode(http.StatusInternalServerError, 10215, "stellaries token 配置的secret 文件key丢失，请联系管理员"),
	SolutionStepNotExist:                        newErrorCode(http.StatusInternalServerError, 10216, "solution step 不存在"),
	DefaultLbNotExist:                           newErrorCode(http.StatusInternalServerError, 10217, "默认负载均衡节点不存在"),
	DiskTypeAutoMasterEtcdPathNullErr:           newErrorCode(http.StatusInternalServerError, 10218, "Master节点选择自动挂载时候ETCD数据盘必填写"),
	DiskTypeAutoSystemDataPathNullErr:           newErrorCode(http.StatusInternalServerError, 10219, "系统节点选择自动挂载时候系统数据盘必填写"),
	DiskTypeAutoDockerPathNullErr:               newErrorCode(http.StatusInternalServerError, 10220, "存在节点未填写Containerd数据盘"),
	DiskTypeAutoKubeletPathNullErr:              newErrorCode(http.StatusInternalServerError, 10221, "存在节点未填写Kubelet数据盘"),
	BaseLineVersionISNull:                       newErrorCode(http.StatusInternalServerError, 10222, "基线版本为空,请确定集群为平台创建"),
	BaseLineVersionNotSupport:                   newErrorCode(http.StatusInternalServerError, 10223, "平台不支持当前基线版本集群升级"),
	PackageNameEmpty:                            newErrorCode(http.StatusInternalServerError, 10224, "升级包名称不能为空"),
	NodeListEmpty:                               newErrorCode(http.StatusInternalServerError, 10225, "节点列表不能为空"),
	LbAddressEmpty:                              newErrorCode(http.StatusInternalServerError, 10226, "负载均衡不能为空"),
	ClusterUpgradeVersionMappingEmpty:           newErrorCode(http.StatusInternalServerError, 10227, "集群升级配置文件缺失,请联系管理员"),
	SisyphusConfigList:                          newErrorCode(http.StatusInternalServerError, 10228, "西西弗斯组件配置丢失"),
	// TemplateCodeIsNotExist
	// 模版文件相关
	TemplateCodeIsNotExist:     newErrorCode(http.StatusInternalServerError, 10300, "模版文件不存在"),
	PortNotEmpty:               newErrorCode(http.StatusInternalServerError, 10301, "端口不能为空"),
	PortIllegal:                newErrorCode(http.StatusInternalServerError, 10302, "请填写正确的端口范围，参考值 0-65535"),
	NodeUsernameNotEmpty:       newErrorCode(http.StatusInternalServerError, 10303, "节点用户名不能为空"),
	NodePasswordNotEmpty:       newErrorCode(http.StatusInternalServerError, 10304, "节点密码不能为空"),
	IsGpuValueIllegal:          newErrorCode(http.StatusInternalServerError, 10305, "是否为GPU节点参数错误,请输入布尔值，参考值true|false"),
	NodeIpIllegal:              newErrorCode(http.StatusInternalServerError, 10306, "节点IP填写错误"),
	NodeIpRepeated:             newErrorCode(http.StatusInternalServerError, 10307, "节点IP重复"),
	AllInOneNodeNumError:       newErrorCode(http.StatusInternalServerError, 10308, "All-In-One模式下请至少提供1台节点"),
	MinimizeHANodeNumError:     newErrorCode(http.StatusInternalServerError, 10309, "最小化高可用模式下请至少提供3台节点"),
	StandardNoneHANodeNumError: newErrorCode(http.StatusInternalServerError, 10310, "标准非高可用模式下请至少提供4台节点"),
	StandardHANodeNumError:     newErrorCode(http.StatusInternalServerError, 10311, "标准高可用模式下请至少提供7台节点"),
	NodeConfigTypeLost:         newErrorCode(http.StatusInternalServerError, 10312, "集群节点配置丢失，请联系管理员"),
	NodeConfigTypeIllegal:      newErrorCode(http.StatusInternalServerError, 10313, "集群节点配置值非法"),
	ClusterMustCreateFail:      newErrorCode(http.StatusInternalServerError, 10314, "操作集群必须为创建失败的集群"),
	NodeOSLost:                 newErrorCode(http.StatusInternalServerError, 10315, "节点操作系统丢失"),
	NodeArchLost:               newErrorCode(http.StatusInternalServerError, 10316, "节点架构丢失"),
	UnKnowArch:                 newErrorCode(http.StatusInternalServerError, 10317, "未知的架构类型"),
	UnKnowOsOrArch:             newErrorCode(http.StatusInternalServerError, 10318, "不存在该架构的操作系统"),
	SolutionVersionCantUsed:    newErrorCode(http.StatusInternalServerError, 10319, "解决方案版本暂不可用"),
	DataEmpty:                  newErrorCode(http.StatusInternalServerError, 10320, "上传内容为空,请填写信息后重新上传"),
	NodeNameNotEmpty:           newErrorCode(http.StatusInternalServerError, 10321, "节点名称不能为空"),
	NodeNameRepeated:           newErrorCode(http.StatusInternalServerError, 10322, "节点名称重复"),
	NodeNotInUpgradeMap:        newErrorCode(http.StatusInternalServerError, 10323, "上传节点中存在节点列表中不存在的节点"),
	NodeIpNotMatch:             newErrorCode(http.StatusInternalServerError, 10324, "节点Ip不能修改"),
	UploadNodeLack:             newErrorCode(http.StatusInternalServerError, 10325, "上传节点缺失节点信息,请重新下载模版填写信息后上传"),

	// 网络相关
	CIDRIllegal:              newErrorCode(http.StatusInternalServerError, 10400, "CIDR格式错误"),
	IPIllegal:                newErrorCode(http.StatusInternalServerError, 10401, "IP不合法"),
	NetworkMaskError:         newErrorCode(http.StatusInternalServerError, 10402, "子网掩码填写错误"),
	IPMaskError:              newErrorCode(http.StatusInternalServerError, 10403, "IP不在掩码表示范围内"),
	StartIpEndIpError:        newErrorCode(http.StatusInternalServerError, 10404, "结束IP需在启始IP之后"),
	ReservedIpFormatError:    newErrorCode(http.StatusInternalServerError, 10405, "保留IP格式错误"),
	ReservedMaskError:        newErrorCode(http.StatusInternalServerError, 10406, "保留IP不在网段内"),
	ReservedNotInStartAndEnd: newErrorCode(http.StatusInternalServerError, 10407, "保留IP不在启始IP与结束IP之间"),
	NetworkCardEmpty:         newErrorCode(http.StatusInternalServerError, 10408, "请输入需要绑定的网卡名称"),

	// 节点上下线相关
	ControlNodeCouldNotEqualsNodeUpDownNode: newErrorCode(http.StatusInternalServerError, 10500, "主控节点与上下线节点不允许为同一台节点"),
	IPRepeat:                                newErrorCode(http.StatusInternalServerError, 10501, "IP重复"),
	BatchInstallerError:                     newErrorCode(http.StatusInternalServerError, 10502, "installer 内部错误，请联系管理员删除脏数据"),
	ControlNodeNotExist:                     newErrorCode(http.StatusInternalServerError, 10503, "主控节点不存在"),
	ExistNodeUpDownNode:                     newErrorCode(http.StatusInternalServerError, 10504, "已存在节点上下线节点"),
	InstallerNotExist:                       newErrorCode(http.StatusInternalServerError, 10505, "Installer 不存在"),
	InstallerRepeated:                       newErrorCode(http.StatusInternalServerError, 10506, "存在多条记录Installer，请联系管理员"),
	NodeUpDownOnlyUpdateInFailedStatus:      newErrorCode(http.StatusInternalServerError, 10507, "节点上下线任务只允许运行失败时更新"),
	NodeUpDownOnlyDeleteInFailedStatus:      newErrorCode(http.StatusInternalServerError, 10508, "节点上下线任务只允许运行失败时删除"),
	NodeUpDownClusterBaselineVersionLost: newErrorCode(http.StatusInternalServerError, 10509, `集群基线版本未初始化，请初始化基线版本。可使用命令 
kubectl label stc {clusterName} caas-infra-baseline={version}
完成集群基线版本初始化`),
	UnKnowBaselineVersion:                            newErrorCode(http.StatusInternalServerError, 10510, "基线版本未适配"),
	NodeUpDownDiskParamMustWriteWhenNodeDiskTypeAuto: newErrorCode(http.StatusInternalServerError, 10511, "当磁盘自动挂载时，上线节点必须填写Docker、Kubelet数据盘"),
	NodeUpNodeMustNotInCluster:                       newErrorCode(http.StatusInternalServerError, 10512, "上线节点不可存在与集群中"),
	NodeDownNodeMustNotInCluster:                     newErrorCode(http.StatusInternalServerError, 10513, "下线节点必须存在与集群中"),

	// 备份服务器
	BackupServerIsUsed:                     newErrorCode(http.StatusInternalServerError, 10529, "备份服务器已分配,无法移除"),
	BucketIsUsed:                           newErrorCode(http.StatusInternalServerError, 10514, "桶已经被使用"),
	AssignFailed:                           newErrorCode(http.StatusInternalServerError, 10515, "分配失败"),
	RemoveFailed:                           newErrorCode(http.StatusInternalServerError, 10516, "移除失败"),
	CreateBackupServerURLFailed:            newErrorCode(http.StatusInternalServerError, 10517, "访问地址校验失败"),
	CreateBackupServerUserOrPasswordFailed: newErrorCode(http.StatusInternalServerError, 10518, "用户名密码校验失败"),
	BackupServerNameRepeat:                 newErrorCode(http.StatusInternalServerError, 10519, "备份服务器名称重复"),
	CreateBucketFailed:                     newErrorCode(http.StatusInternalServerError, 10520, "创建桶失败"),
	UpdateBucketFailed:                     newErrorCode(http.StatusInternalServerError, 10521, "更新桶失败"),
	DeleteBucketFailed:                     newErrorCode(http.StatusInternalServerError, 10522, "删除桶失败"),
	CheckBucketFailed:                      newErrorCode(http.StatusInternalServerError, 10523, "检查桶失败"),
	CreateBackupServerIPAndPortFailed:      newErrorCode(http.StatusInternalServerError, 10524, "访问地址不可重复"),
	BucketNameRepeat:                       newErrorCode(http.StatusInternalServerError, 10525, "存储桶名称在同个备份服务器下不可重复"),
	BackupServerIsUsedByOrgan:              newErrorCode(http.StatusInternalServerError, 10526, "备份服务器已被租户使用，无法移除"),
	BackupServerIsUsedByProject:            newErrorCode(http.StatusInternalServerError, 10527, "备份服务器已被项目使用，无法移除"),
	BucketIsUsedByProject:                  newErrorCode(http.StatusInternalServerError, 10528, "当前存储桶已被租户下项目使用，无法取消分配"),
	BucketLimiterWithCurrentStorage:        newErrorCode(http.StatusInternalServerError, 10629, "配额上限应大于当前用量"),

	// 数据中心
	DataCenterIdIsNull:    newErrorCode(http.StatusInternalServerError, 10600, "数据中心id不能为空"),
	DataCenterNameExisted: newErrorCode(http.StatusInternalServerError, 10601, "数据中心名称已经存在"),
	DataCenterCodeExisted: newErrorCode(http.StatusInternalServerError, 10602, "数据中心标识已经存在"),
	DataCenterNameIsNull:  newErrorCode(http.StatusInternalServerError, 10603, "数据中心名字不能为空"),
	DataCenterCodeIsNull:  newErrorCode(http.StatusInternalServerError, 10604, "数据中心标识不能为空"),
	DataCenterHaveUnit:    newErrorCode(http.StatusInternalServerError, 10605, "数据中心下存在逻辑单元"),

	// 逻辑单元
	LogicalUnitNameExisted:     newErrorCode(http.StatusInternalServerError, 10605, "逻辑单元名称已经存在"),
	LogicalUnitCodeExisted:     newErrorCode(http.StatusInternalServerError, 10606, "逻辑单元标识已经存在"),
	LogicalUnitNameIsNull:      newErrorCode(http.StatusInternalServerError, 10607, "逻辑单元名称不能为空"),
	LogicalUnitCodeIsNull:      newErrorCode(http.StatusInternalServerError, 10608, "逻辑单元标识不能为空"),
	LogicalUnitTypeIsNull:      newErrorCode(http.StatusInternalServerError, 10609, "逻辑单元类型不能为空"),
	LogicalUnitTerritoryIsNull: newErrorCode(http.StatusInternalServerError, 10610, "逻辑单元所处地域不能为空"),
	LogicalUnitIdIsNull:        newErrorCode(http.StatusInternalServerError, 10611, "逻辑单元id不能为空"),
	LogicalUnitHaveData:        newErrorCode(http.StatusInternalServerError, 10612, "逻辑单元下存在绑定数据"),

	// 逻辑单元绑定信息
	LogicalInfoInfoIsNull: newErrorCode(http.StatusInternalServerError, 10620, "逻辑单元信息info不能为空"),
	LogicalInfoTypeIsNull: newErrorCode(http.StatusInternalServerError, 10621, "逻辑单元信息类型不能为空"),
	ClusterNameIsNull:     newErrorCode(http.StatusInternalServerError, 10622, "集群名称不能为空"),

	// baseline error code
	BaselineCategoryNameRepeat:                 newErrorCode(http.StatusInternalServerError, 10700, "分类名称已存在，请重新命名"),
	BaselineStandardNameRepeat:                 newErrorCode(http.StatusInternalServerError, 10701, "基线标准名称已存在，请重新命名"),
	BaselineStandardInUse:                      newErrorCode(http.StatusInternalServerError, 10702, "基线标准被检查策略引用，无法删除"),
	BaselineCheckerNotFound:                    newErrorCode(http.StatusInternalServerError, 10703, "检查项不存在"),
	BaselineCheckerSaveError:                   newErrorCode(http.StatusInternalServerError, 10704, "检查项保存失败"),
	BaselineCheckerNameRepeat:                  newErrorCode(http.StatusInternalServerError, 10705, "检查项名称重复"),
	BaselineGroupHasStandards:                  newErrorCode(http.StatusInternalServerError, 10706, "基线标准分类存在基线标准，无法删除"),
	BaselineCustomCheckersBindingStandardError: newErrorCode(http.StatusInternalServerError, 10707, "自定义检查项被基线标准引用，无法删除"),
	BaselineCustomCheckerNameRepeat:            newErrorCode(http.StatusInternalServerError, 10708, "自定义检查项名称已存在"),
	BaselineStrategyNameRepeat:                 newErrorCode(http.StatusInternalServerError, 10709, "检查策略名称已存在"),
	BaselineCronExpressionInvalid:              newErrorCode(http.StatusInternalServerError, 10710, "非法的Cron表达式"),
	BaselineStrategyCleanResourcesFailed:       newErrorCode(http.StatusInternalServerError, 10711, "清除策略剩余资源失败"),
	BaselineStrategyUnbindMustHasOneStandard:   newErrorCode(http.StatusInternalServerError, 10712, "%s 策略无法解绑，策略至少需要一个标准"),
	BaselineRecurringParamsInvalid:             newErrorCode(http.StatusInternalServerError, 10713, "周期执行参数无效"),
	BaselineCustomCheckerUploadInvalid:         newErrorCode(http.StatusInternalServerError, 10714, "检查项上传文件无效"),
	BaselineCustomCheckerConfigInvalid:         newErrorCode(http.StatusInternalServerError, 10715, "检查项配置无效"),
	BaselineCustomCheckerConfigFileInvalidJSON: newErrorCode(http.StatusInternalServerError, 10716, "JSON文件内容格式错误"),
	BaselineCustomCheckerConfigFileInvalidYAML: newErrorCode(http.StatusInternalServerError, 10717, "YAML文件内容格式错误"),
	BaselineHighRiskCheckItemsNotPassed:        newErrorCode(http.StatusInternalServerError, 10718, "高风险检查项未通过"),
	BaselineMediumRiskCheckItemsNotPassed:      newErrorCode(http.StatusInternalServerError, 10719, "中风险检查项未通过"),
	BaselineLowRiskCheckItemsNotPassed:         newErrorCode(http.StatusInternalServerError, 10720, "低风险检查项未通过"),
	BaselineSomeCheckItemsNotPassed:            newErrorCode(http.StatusInternalServerError, 10721, "部分检查项未通过"),
	BaselineFeatureNotEnabled:                  newErrorCode(http.StatusInternalServerError, 10722, "基线检查功能未开启"),
	BaselineRunStrategyCheckJobFailedGetLock:   newErrorCode(http.StatusInternalServerError, 10723, "基线策略检查任务执行获取锁失败,已有其他任务执行"),
	BaselineRunStrategyJobAlreadyRunning:       newErrorCode(http.StatusInternalServerError, 10724, "基线策略检查任务已运行"),

	// RBAC模块错误码 11000-11999
	RBACRoleNotFound:                    newErrorCode(http.StatusNotFound, 11001, "角色不存在"),
	RBACRoleAlreadyExists:               newErrorCode(http.StatusConflict, 11002, "角色已存在"),
	RBACRoleInUse:                       newErrorCode(http.StatusConflict, 11003, "角色正在被使用，无法删除"),
	RBACClusterRoleNotFound:             newErrorCode(http.StatusNotFound, 11004, "集群角色不存在"),
	RBACClusterRoleAlreadyExists:        newErrorCode(http.StatusConflict, 11005, "集群角色已存在"),
	RBACClusterRoleInUse:                newErrorCode(http.StatusConflict, 11006, "集群角色正在被使用，无法删除"),
	RBACRoleBindingNotFound:             newErrorCode(http.StatusNotFound, 11007, "角色绑定不存在"),
	RBACRoleBindingAlreadyExists:        newErrorCode(http.StatusConflict, 11008, "角色绑定已存在"),
	RBACClusterRoleBindingNotFound:      newErrorCode(http.StatusNotFound, 11009, "集群角色绑定不存在"),
	RBACClusterRoleBindingAlreadyExists: newErrorCode(http.StatusConflict, 11010, "集群角色绑定已存在"),
	RBACServiceAccountNotFound:          newErrorCode(http.StatusNotFound, 11011, "服务账号不存在"),
	RBACServiceAccountAlreadyExists:     newErrorCode(http.StatusConflict, 11012, "服务账号已存在"),
	RBACServiceAccountProtected:         newErrorCode(http.StatusForbidden, 11013, "服务账号受保护，无法删除"),
	RBACSystemProtectedResource:         newErrorCode(http.StatusForbidden, 11014, "系统保护资源，无法删除"),
	RBACInvalidSubjectKind:              newErrorCode(http.StatusBadRequest, 11015, "无效的主体类型"),
	RBACInvalidRoleRef:                  newErrorCode(http.StatusBadRequest, 11016, "无效的角色引用"),
	RBACClusterNotFound:                 newErrorCode(http.StatusNotFound, 11017, "集群不存在"),
	RBACNamespaceNotFound:               newErrorCode(http.StatusNotFound, 11018, "命名空间不存在"),
	RBACInvalidPolicyRule:               newErrorCode(http.StatusBadRequest, 11019, "无效的策略规则"),
	RBACOperationForbidden:              newErrorCode(http.StatusForbidden, 11020, "操作被禁止"),
}

type errorCodeVar struct {

	// Kubernetes Error
	K8sUnauthorized                ErrorCode
	K8sForbidden                   ErrorCode
	K8sNotFound                    ErrorCode
	K8sAlreadyExists               ErrorCode
	K8sConflict                    ErrorCode
	K8sGone                        ErrorCode
	K8sInvalid                     ErrorCode
	K8sServerTimeout               ErrorCode
	K8sTimeout                     ErrorCode
	K8sTooManyRequests             ErrorCode
	K8sBadRequest                  ErrorCode
	K8sMethodNotAllowed            ErrorCode
	K8sNotAcceptable               ErrorCode
	K8sRequestEntityTooLarge       ErrorCode
	K8sUnsupportedMediaType        ErrorCode
	K8sInternalError               ErrorCode
	K8sExpired                     ErrorCode
	K8sServiceUnavailable          ErrorCode
	K8sError                       ErrorCode
	K8sIsDeleting                  ErrorCode
	K8sForbiddenDeletedByFinalizer ErrorCode

	UnKnow                 ErrorCode
	ResourceNotFount       ErrorCode
	AuthForbidden          ErrorCode
	InValidToken           ErrorCode
	ClusterNotExist        ErrorCode
	ClusterStatusNotOnline ErrorCode
	ParamError             ErrorCode
	ConnectFailure         ErrorCode
	BoolParamError         ErrorCode

	// IllegalUserStatus
	// 用户相关
	IllegalUserStatus ErrorCode
	UserUnLogin       ErrorCode
	IllegalUserStruct ErrorCode
	TokenEffective    ErrorCode
	TokenExpire       ErrorCode

	// CloudServiceListResourcesError  resource list error
	CloudServiceListResourcesError ErrorCode

	// AsyncJobForcedStop
	// AsyncJobTimeout
	// AsyncError
	// 异步任务相关
	AsyncJobForcedStop ErrorCode
	AsyncJobTimeout    ErrorCode
	AsyncError         ErrorCode

	// StorageServerNameRepeat
	// StorageServerUsages
	// DownloadURLTimeout
	// DownloadURLError
	// SchedulesNameRepeat
	// RestoresTemplateNameRepeat
	// SchedulesIsUsed
	// RestoreTemplateNotFound
	// velero 相关参数
	StorageServerNameRepeat    ErrorCode
	StorageServerUsages        ErrorCode
	DownloadURLTimeout         ErrorCode
	DownloadURLError           ErrorCode
	SchedulesNameRepeat        ErrorCode
	RestoresTemplateNameRepeat ErrorCode
	SchedulesIsUsed            ErrorCode
	RestoreTemplateNotFound    ErrorCode
	StorageServerNotExist      ErrorCode

	// ClusterManaged
	// ClusterCreating
	// 集群相关
	ClusterNameRepeated                         ErrorCode
	IllegalNodeUsername                         ErrorCode
	IllegalNodePassword                         ErrorCode
	SolutionNameIsNotSet                        ErrorCode
	UnKnowAuthType                              ErrorCode
	InstallerLabelKeyEmpty                      ErrorCode
	ClusterInstallerNumberError                 ErrorCode
	ClusterCreateTaskNotExist                   ErrorCode
	ClusterCouldNotDeleteByStatus               ErrorCode
	StellarisStandbyValueNotNullOnActiveStandBy ErrorCode
	ChooseOneContainerNetworkSolutionAtLest     ErrorCode
	ApiServerAddressRequiredInHAModel           ErrorCode
	DefaultLBAddressRequiredInHAModel           ErrorCode
	StellariesSecretLost                        ErrorCode
	StellariesSecretKeyLost                     ErrorCode
	SolutionStepNotExist                        ErrorCode
	DefaultLbNotExist                           ErrorCode
	DiskTypeAutoMasterEtcdPathNullErr           ErrorCode
	DiskTypeAutoSystemDataPathNullErr           ErrorCode
	DiskTypeAutoDockerPathNullErr               ErrorCode
	DiskTypeAutoKubeletPathNullErr              ErrorCode
	BaseLineVersionISNull                       ErrorCode
	BaseLineVersionNotSupport                   ErrorCode
	PackageNameEmpty                            ErrorCode
	NodeListEmpty                               ErrorCode
	LbAddressEmpty                              ErrorCode
	ClusterUpgradeVersionMappingEmpty           ErrorCode
	SisyphusConfigList                          ErrorCode

	// TemplateCodeIsNotExist
	// 模版文件相关
	TemplateCodeIsNotExist     ErrorCode
	PortNotEmpty               ErrorCode
	PortIllegal                ErrorCode
	NodeUsernameNotEmpty       ErrorCode
	NodePasswordNotEmpty       ErrorCode
	IsGpuValueIllegal          ErrorCode
	NodeIpIllegal              ErrorCode
	NodeIpRepeated             ErrorCode
	AllInOneNodeNumError       ErrorCode
	MinimizeHANodeNumError     ErrorCode
	StandardNoneHANodeNumError ErrorCode
	StandardHANodeNumError     ErrorCode
	NodeConfigTypeLost         ErrorCode
	NodeConfigTypeIllegal      ErrorCode
	ClusterMustCreateFail      ErrorCode
	NodeOSLost                 ErrorCode
	NodeArchLost               ErrorCode
	UnKnowArch                 ErrorCode
	UnKnowOsOrArch             ErrorCode
	SolutionVersionCantUsed    ErrorCode
	DataEmpty                  ErrorCode
	NodeNameNotEmpty           ErrorCode
	NodeNameRepeated           ErrorCode
	NodeNotInUpgradeMap        ErrorCode
	NodeIpNotMatch             ErrorCode
	UploadNodeLack             ErrorCode
	// 网络相关
	CIDRIllegal              ErrorCode
	IPIllegal                ErrorCode
	NetworkMaskError         ErrorCode
	IPMaskError              ErrorCode
	StartIpEndIpError        ErrorCode
	ReservedIpFormatError    ErrorCode
	ReservedMaskError        ErrorCode
	ReservedNotInStartAndEnd ErrorCode
	NetworkCardEmpty         ErrorCode

	// 节点上下线相关
	ControlNodeCouldNotEqualsNodeUpDownNode          ErrorCode
	IPRepeat                                         ErrorCode
	BatchInstallerError                              ErrorCode
	ControlNodeNotExist                              ErrorCode
	ExistNodeUpDownNode                              ErrorCode
	InstallerNotExist                                ErrorCode
	InstallerRepeated                                ErrorCode
	NodeUpDownOnlyUpdateInFailedStatus               ErrorCode
	NodeUpDownOnlyDeleteInFailedStatus               ErrorCode
	NodeUpDownClusterBaselineVersionLost             ErrorCode
	UnKnowBaselineVersion                            ErrorCode
	NodeUpDownDiskParamMustWriteWhenNodeDiskTypeAuto ErrorCode
	NodeUpNodeMustNotInCluster                       ErrorCode
	NodeDownNodeMustNotInCluster                     ErrorCode
	// 备份服务器
	BackupServerIsUsed                     ErrorCode
	BucketIsUsed                           ErrorCode
	AssignFailed                           ErrorCode
	RemoveFailed                           ErrorCode
	CreateBackupServerURLFailed            ErrorCode
	CreateBackupServerUserOrPasswordFailed ErrorCode
	BackupServerNameRepeat                 ErrorCode
	CreateBucketFailed                     ErrorCode
	UpdateBucketFailed                     ErrorCode
	DeleteBucketFailed                     ErrorCode
	CheckBucketFailed                      ErrorCode
	CreateBackupServerIPAndPortFailed      ErrorCode
	BucketNameRepeat                       ErrorCode
	BackupServerIsUsedByOrgan              ErrorCode
	BackupServerIsUsedByProject            ErrorCode
	BucketIsUsedByProject                  ErrorCode

	// baseline
	BaselineCategoryNameRepeat                 ErrorCode
	BaselineStandardNameRepeat                 ErrorCode
	BaselineStandardInUse                      ErrorCode
	BaselineCheckerNotFound                    ErrorCode
	BaselineCheckerSaveError                   ErrorCode
	BaselineCheckerNameRepeat                  ErrorCode
	BaselineGroupHasStandards                  ErrorCode
	BaselineCustomCheckersBindingStandardError ErrorCode
	BaselineCustomCheckerNameRepeat            ErrorCode
	BaselineStrategyNameRepeat                 ErrorCode
	BaselineCronExpressionInvalid              ErrorCode
	BaselineStrategyCleanResourcesFailed       ErrorCode
	BaselineStrategyUnbindMustHasOneStandard   ErrorCode
	BaselineRecurringParamsInvalid             ErrorCode
	BaselineCustomCheckerUploadInvalid         ErrorCode
	BaselineCustomCheckerConfigInvalid         ErrorCode
	BaselineCustomCheckerConfigFileInvalidJSON ErrorCode
	BaselineCustomCheckerConfigFileInvalidYAML ErrorCode
	BaselineHighRiskCheckItemsNotPassed        ErrorCode
	BaselineMediumRiskCheckItemsNotPassed      ErrorCode
	BaselineLowRiskCheckItemsNotPassed         ErrorCode
	BaselineSomeCheckItemsNotPassed            ErrorCode
	BaselineFeatureNotEnabled                  ErrorCode
	BaselineRunStrategyCheckJobFailedGetLock   ErrorCode
	BaselineRunStrategyJobAlreadyRunning       ErrorCode
	// 数据中心
	DataCenterNameExisted ErrorCode
	DataCenterCodeExisted ErrorCode
	DataCenterNameIsNull  ErrorCode
	DataCenterCodeIsNull  ErrorCode
	DataCenterIdIsNull    ErrorCode
	DataCenterHaveUnit    ErrorCode
	// 逻辑单元
	LogicalUnitNameExisted     ErrorCode
	LogicalUnitCodeExisted     ErrorCode
	LogicalUnitNameIsNull      ErrorCode
	LogicalUnitCodeIsNull      ErrorCode
	LogicalUnitTypeIsNull      ErrorCode
	LogicalUnitTerritoryIsNull ErrorCode
	LogicalUnitIdIsNull        ErrorCode
	LogicalUnitHaveData        ErrorCode
	// 逻辑单元信息
	LogicalInfoInfoIsNull           ErrorCode
	LogicalInfoTypeIsNull           ErrorCode
	ClusterNameIsNull               ErrorCode
	BucketLimiterWithCurrentStorage ErrorCode

	// RBAC模块错误码 11000-11999
	RBACRoleNotFound                    ErrorCode
	RBACRoleAlreadyExists               ErrorCode
	RBACRoleInUse                       ErrorCode
	RBACClusterRoleNotFound             ErrorCode
	RBACClusterRoleAlreadyExists        ErrorCode
	RBACClusterRoleInUse                ErrorCode
	RBACRoleBindingNotFound             ErrorCode
	RBACRoleBindingAlreadyExists        ErrorCode
	RBACClusterRoleBindingNotFound      ErrorCode
	RBACClusterRoleBindingAlreadyExists ErrorCode
	RBACServiceAccountNotFound          ErrorCode
	RBACServiceAccountAlreadyExists     ErrorCode
	RBACServiceAccountProtected         ErrorCode
	RBACSystemProtectedResource         ErrorCode
	RBACInvalidSubjectKind              ErrorCode
	RBACInvalidRoleRef                  ErrorCode
	RBACClusterNotFound                 ErrorCode
	RBACNamespaceNotFound               ErrorCode
	RBACInvalidPolicyRule               ErrorCode
	RBACOperationForbidden              ErrorCode
}
